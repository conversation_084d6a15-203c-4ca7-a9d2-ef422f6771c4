import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/user_provider.dart';
import '../services/auth_service.dart';
import '../screens/login_screen.dart';
import '../screens/home_screen.dart';
import '../screens/member_main_screen.dart';

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  final AuthenticationService _authService = AuthenticationService();
  bool _isInitializing = true;

  @override
  void initState() {
    super.initState();
    _initializeAuth();
  }

  Future<void> _initializeAuth() async {
    try {
      // Check if user is already authenticated
      final currentUser = _authService.currentUser;

      if (currentUser != null) {
        // User is authenticated, load their data
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        await userProvider.loadUser();
      }
    } catch (e) {
      print('Error initializing auth: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isInitializing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isInitializing) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return StreamBuilder(
      stream: _authService.authStateChanges,
      builder: (context, snapshot) {
        // Show loading while waiting for auth state
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        // User is not authenticated
        if (!snapshot.hasData || snapshot.data == null) {
          // Clear user data when user signs out
          WidgetsBinding.instance.addPostFrameCallback((_) {
            final userProvider =
                Provider.of<UserProvider>(context, listen: false);
            userProvider.clearUserData();
          });
          return const LoginScreen();
        }

        // User is authenticated, load their data
        return Consumer<UserProvider>(
          builder: (context, userProvider, child) {
            final currentUser = snapshot.data!;

            // Check if we need to load user data for a new user
            if (userProvider.user == null ||
                userProvider.user!.id != currentUser.uid) {
              // Clear previous user data and load new user
              WidgetsBinding.instance.addPostFrameCallback((_) async {
                userProvider.clearUserData();
                await userProvider.loadUser();
              });

              return const Scaffold(
                body: Center(
                  child: CircularProgressIndicator(),
                ),
              );
            }

            // Show loading while user data is being loaded
            if (userProvider.isLoading) {
              return const Scaffold(
                body: Center(
                  child: CircularProgressIndicator(),
                ),
              );
            }

            // User has approved gym membership
            if (userProvider.hasApprovedGym) {
              return const MemberMainScreen();
            }

            // User is not verified or has no gym
            return const HomeScreen();
          },
        );
      },
    );
  }
}
