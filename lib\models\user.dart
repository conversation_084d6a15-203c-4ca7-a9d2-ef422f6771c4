import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String id;
  final String name;
  final String email;
  final String? gymId;
  final bool isVerified;
  final String gymIdStatus; // "pending", "approved", or null

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    this.gymId,
    required this.isVerified,
    this.gymIdStatus = '',
  });

  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserModel(
      id: doc.id,
      name: data['name'] ?? '',
      email: data['email'] ?? '',
      gymId: data['gymId'],
      isVerified: data['isVerified'] ?? false,
      gymIdStatus: data['gymIdStatus'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'email': email,
      'gymId': gymId,
      'isVerified': isVerified,
      'gymIdStatus': gymIdStatus,
    };
  }

  UserModel copyWith({
    String? name,
    String? email,
    String? gymId,
    bool? isVerified,
    String? gymIdStatus,
  }) {
    return UserModel(
      id: id,
      name: name ?? this.name,
      email: email ?? this.email,
      gymId: gymId ?? this.gymId,
      isVerified: isVerified ?? this.isVerified,
      gymIdStatus: gymIdStatus ?? this.gymIdStatus,
    );
  }
}
