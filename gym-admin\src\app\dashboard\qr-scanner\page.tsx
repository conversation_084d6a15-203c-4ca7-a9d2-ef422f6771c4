'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { db } from '@/lib/firebase';
import { doc, getDoc, collection, addDoc, updateDoc, increment, Timestamp } from 'firebase/firestore';
import DashboardLayout from '@/components/DashboardLayout';
import { QrReader } from 'react-qr-reader';
import toast from 'react-hot-toast';

interface UserData {
  id: string;
  name: string;
  email: string;
  gymId: string;
  isVerified: boolean;
  subscription?: {
    startDate: Date;
    endDate: Date;
    status: string;
  };
}

interface ValidationResult {
  isValid: boolean;
  message: string;
  userData?: UserData;
}

export default function QRScannerPage() {
  const { user } = useAuth();
  const [scanResult, setScanResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Validate QR code and process check-in
  const validateAndProcessQR = async (qrData: string) => {
    if (!user?.gymId) {
      setValidationResult({
        isValid: false,
        message: 'Staff user has no gym associated',
      });
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // Parse QR code data
      const payload = JSON.parse(qrData);
      const { uid, date, hash } = payload;

      if (!uid || !date || !hash) {
        setValidationResult({
          isValid: false,
          message: 'Invalid QR code format',
        });
        return;
      }

      // Check if QR code is for today
      const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
      if (date !== today) {
        setValidationResult({
          isValid: false,
          message: 'QR code has expired (not for today)',
        });
        return;
      }

      // Fetch user data
      const userDoc = await getDoc(doc(db, 'users', uid));
      if (!userDoc.exists()) {
        setValidationResult({
          isValid: false,
          message: 'User not found',
        });
        return;
      }

      const userData = userDoc.data();

      // Validate user belongs to this gym
      if (userData.gymId !== user.gymId) {
        setValidationResult({
          isValid: false,
          message: 'User is not subscribed to this gym',
          userData: {
            id: uid,
            name: userData.name || 'Unknown',
            email: userData.email || 'Unknown',
            gymId: userData.gymId || '',
            isVerified: userData.isVerified || false,
          },
        });
        return;
      }

      // Validate user is verified
      if (!userData.isVerified) {
        setValidationResult({
          isValid: false,
          message: 'User subscription is not verified/active',
          userData: {
            id: uid,
            name: userData.name || 'Unknown',
            email: userData.email || 'Unknown',
            gymId: userData.gymId || '',
            isVerified: userData.isVerified || false,
          },
        });
        return;
      }

      // Check subscription validity
      const subscriptionDoc = await getDoc(doc(db, 'subscriptions', uid));
      let subscriptionData = null;

      if (subscriptionDoc.exists()) {
        const subData = subscriptionDoc.data();
        const endDate = subData.endDate?.toDate();

        if (endDate && new Date() > endDate) {
          setValidationResult({
            isValid: false,
            message: 'User subscription has expired',
            userData: {
              id: uid,
              name: userData.name || 'Unknown',
              email: userData.email || 'Unknown',
              gymId: userData.gymId || '',
              isVerified: userData.isVerified || false,
              subscription: {
                startDate: subData.startDate?.toDate() || new Date(),
                endDate: endDate,
                status: subData.status || 'unknown',
              },
            },
          });
          return;
        }

        subscriptionData = {
          startDate: subData.startDate?.toDate() || new Date(),
          endDate: endDate || new Date(),
          status: subData.status || 'active',
        };
      }

      // All validations passed - log attendance
      await logAttendance(uid, user.gymId);

      setValidationResult({
        isValid: true,
        message: 'Check-in successful!',
        userData: {
          id: uid,
          name: userData.name || 'Unknown',
          email: userData.email || 'Unknown',
          gymId: userData.gymId || '',
          isVerified: userData.isVerified || false,
          subscription: subscriptionData || undefined,
        },
      });

      toast.success(`Welcome ${userData.name || 'Member'}! Check-in successful.`);

    } catch (error) {
      console.error('QR validation error:', error);
      setValidationResult({
        isValid: false,
        message: 'Failed to validate QR code. Please try again.',
      });
      toast.error('Failed to process QR code');
    } finally {
      setIsProcessing(false);
    }
  };

  // Log attendance to Firestore
  const logAttendance = async (userId: string, gymId: string) => {
    try {
      // Add attendance log
      await addDoc(collection(db, 'users', userId, 'attendanceLogs'), {
        timestamp: Timestamp.now(),
        gymId: gymId,
        date: new Date().toISOString().split('T')[0],
      });

      // Increment gym user count
      await updateDoc(doc(db, 'gyms', gymId), {
        currentUserCount: increment(1),
      });

      // Set timer to decrement count after 1 hour (3600000 ms)
      setTimeout(async () => {
        try {
          await updateDoc(doc(db, 'gyms', gymId), {
            currentUserCount: increment(-1),
          });
        } catch (error) {
          console.error('Failed to decrement user count:', error);
        }
      }, 3600000); // 1 hour

    } catch (error) {
      console.error('Failed to log attendance:', error);
      throw error;
    }
  };

  const handleScan = (result: any) => {
    if (result && !isProcessing) {
      setScanResult(result?.text);
      validateAndProcessQR(result?.text);
    }
  };

  const handleError = (error: any) => {
    setError(error?.message || 'An error occurred while scanning');
  };

  const resetScanner = () => {
    setScanResult(null);
    setValidationResult(null);
    setError(null);
  };

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900">QR Code Scanner</h1>
            <p className="mt-2 text-sm text-gray-700">
              Scan QR codes to check in members and verify their membership status.
            </p>
          </div>
        </div>

        <div className="mt-6 bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="max-w-2xl mx-auto">
              {/* QR Scanner */}
              <div className="mb-6">
                <div className="aspect-w-1 aspect-h-1 max-w-md mx-auto">
                  <QrReader
                    constraints={{ facingMode: 'environment' }}
                    onResult={handleScan}
                    className="w-full h-full rounded-lg"
                  />
                </div>

                {isProcessing && (
                  <div className="mt-4 text-center">
                    <div className="inline-flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600 mr-2"></div>
                      <span className="text-sm text-gray-600">Processing QR code...</span>
                    </div>
                  </div>
                )}
              </div>

              {/* Error Display */}
              {error && (
                <div className="mb-4 p-4 bg-red-50 rounded-md border border-red-200">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">Scanner Error</h3>
                      <p className="mt-1 text-sm text-red-700">{error}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Validation Results */}
              {validationResult && (
                <div className={`p-6 rounded-lg border-2 ${
                  validationResult.isValid
                    ? 'bg-green-50 border-green-200'
                    : 'bg-red-50 border-red-200'
                }`}>
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      {validationResult.isValid ? (
                        <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      ) : (
                        <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      )}
                    </div>
                    <div className="ml-3 flex-1">
                      <h3 className={`text-lg font-medium ${
                        validationResult.isValid ? 'text-green-800' : 'text-red-800'
                      }`}>
                        {validationResult.isValid ? 'Check-in Successful!' : 'Check-in Failed'}
                      </h3>
                      <p className={`mt-1 text-sm ${
                        validationResult.isValid ? 'text-green-700' : 'text-red-700'
                      }`}>
                        {validationResult.message}
                      </p>

                      {/* User Details */}
                      {validationResult.userData && (
                        <div className="mt-4 bg-white rounded-md p-4 border">
                          <h4 className="text-sm font-medium text-gray-900 mb-3">Member Details</h4>
                          <dl className="grid grid-cols-1 gap-x-4 gap-y-3 sm:grid-cols-2">
                            <div>
                              <dt className="text-xs font-medium text-gray-500 uppercase tracking-wide">Name</dt>
                              <dd className="mt-1 text-sm text-gray-900">{validationResult.userData.name}</dd>
                            </div>
                            <div>
                              <dt className="text-xs font-medium text-gray-500 uppercase tracking-wide">Email</dt>
                              <dd className="mt-1 text-sm text-gray-900">{validationResult.userData.email}</dd>
                            </div>
                            <div>
                              <dt className="text-xs font-medium text-gray-500 uppercase tracking-wide">Status</dt>
                              <dd className="mt-1">
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                  validationResult.userData.isVerified
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-red-100 text-red-800'
                                }`}>
                                  {validationResult.userData.isVerified ? 'Verified' : 'Not Verified'}
                                </span>
                              </dd>
                            </div>
                            {validationResult.userData.subscription && (
                              <div>
                                <dt className="text-xs font-medium text-gray-500 uppercase tracking-wide">Subscription</dt>
                                <dd className="mt-1 text-sm text-gray-900">
                                  {validationResult.userData.subscription.startDate.toLocaleDateString()} - {validationResult.userData.subscription.endDate.toLocaleDateString()}
                                </dd>
                              </div>
                            )}
                          </dl>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Reset Button */}
                  <div className="mt-4 flex justify-end">
                    <button
                      onClick={resetScanner}
                      className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      Scan Another Code
                    </button>
                  </div>
                </div>
              )}

              {/* Instructions */}
              {!validationResult && !isProcessing && (
                <div className="text-center text-gray-500">
                  <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 16h4.01M12 8h4.01" />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">Ready to Scan</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Position a member's QR code in front of the camera to check them in.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}