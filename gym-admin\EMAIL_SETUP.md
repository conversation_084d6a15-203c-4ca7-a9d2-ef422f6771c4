# Email Notification Setup Guide

This guide explains how to set up email notifications for gym membership approvals and rejections.

## Overview

The email notification system sends automated emails to users when:
- Their gym membership request is **approved** by gym staff
- Their gym membership request is **rejected** by gym staff

## Setup Instructions

### 1. Environment Variables

Create a `.env.local` file in the `gym-admin` directory with the following variables:

```bash
# Email Configuration (Gmail SMTP - Recommended for development)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Email settings
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Your Gym Name

# App settings
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 2. Gmail Setup (Recommended)

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate an App Password**:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate a new app password for "Mail"
   - Use this password in `EMAIL_PASS`

### 3. Alternative: SendGrid Setup

If you prefer SendGrid:

```bash
EMAIL_HOST=smtp.sendgrid.net
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=apikey
EMAIL_PASS=your-sendgrid-api-key
```

## Testing the Email System

### 1. Test Email Configuration

```bash
# Test if email configuration is valid
curl -X GET http://localhost:3000/api/email/test

# Send a test email
curl -X POST http://localhost:3000/api/email/test \
  -H "Content-Type: application/json" \
  -d '{"testEmail": "<EMAIL>"}'
```

### 2. Test Approval Email

```bash
curl -X POST http://localhost:3000/api/email/membership-approved \
  -H "Content-Type: application/json" \
  -d '{
    "userEmail": "<EMAIL>",
    "userName": "John Doe",
    "gymName": "Test Gym"
  }'
```

### 3. Test Rejection Email

```bash
curl -X POST http://localhost:3000/api/email/membership-rejected \
  -H "Content-Type: application/json" \
  -d '{
    "userEmail": "<EMAIL>",
    "userName": "John Doe",
    "gymName": "Test Gym",
    "reason": "Membership capacity reached"
  }'
```

## How It Works

1. **User Flow**:
   - User selects a gym in the mobile app
   - User's request appears in gym staff dashboard as "pending"

2. **Staff Flow**:
   - Gym staff logs into admin dashboard
   - Reviews pending membership requests
   - Clicks "Accept" or "Deny" for each user

3. **Email Flow**:
   - System automatically sends email notification
   - User receives beautifully formatted email
   - Email includes next steps and relevant information

## Email Templates

### Approval Email Features:
- ✅ Congratulatory message
- ✅ Gym name and welcome message
- ✅ Next steps (open app, generate QR code)
- ✅ Professional HTML formatting

### Rejection Email Features:
- ✅ Polite rejection message
- ✅ Optional reason for rejection
- ✅ Guidance for next steps
- ✅ Professional HTML formatting

## Troubleshooting

### Common Issues:

1. **"Authentication failed"**
   - Check Gmail app password is correct
   - Ensure 2FA is enabled on Gmail account

2. **"Connection timeout"**
   - Check EMAIL_HOST and EMAIL_PORT settings
   - Verify firewall/network settings

3. **"Email not received"**
   - Check spam/junk folder
   - Verify email address is correct
   - Test with different email provider

### Debug Mode:

Check the Next.js console for detailed error messages when emails fail to send.

## Security Notes

- Never commit `.env.local` to version control
- Use app passwords, not your main Gmail password
- Consider using a dedicated email account for the gym system
- For production, consider using a professional email service like SendGrid

## API Endpoints

- `GET /api/email/test` - Test email configuration
- `POST /api/email/test` - Send test email
- `POST /api/email/membership-approved` - Send approval email
- `POST /api/email/membership-rejected` - Send rejection email
