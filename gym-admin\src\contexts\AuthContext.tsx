'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import {
  signInWithEmailAndPassword,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  User
} from 'firebase/auth';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';

interface StaffUser extends User {
  gymId?: string;
}

interface AuthContextType {
  user: StaffUser | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  signIn: async () => {},
  signOut: async () => {},
});

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<StaffUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        // Fetch additional staff data from Firestore
        const staffDocRef = doc(db, 'gymStaffs', user.uid);
        const staffDoc = await getDoc(staffDocRef);
        const staffData = staffDoc.data();

        // If staff document doesn't exist, create it
        if (!staffDoc.exists()) {
          if (confirm('No staff document found for your account. Would you like to create one?')) {
            try {
              // Create the staff document
              await setDoc(staffDocRef, {
                email: user.email || '<EMAIL>',
                password: 'Abc12345' // This is just for display in Firestore, not for authentication
              });

              // Reload the page to apply changes
              window.location.reload();
              return;
            } catch (error) {
              console.error('Error creating staff document:', error);
            }
          }
        }

        // Make sure gymId is a string and not undefined or null
        let gymId = staffData?.gymId;

        // If gymId exists but is not a string, convert it to string
        if (gymId !== undefined && gymId !== null && typeof gymId !== 'string') {
          gymId = String(gymId);
        }

        // Extend the user object with gymId
        const staffUser: StaffUser = Object.assign(user, {
          gymId: gymId,
        });

        setUser(staffUser);
      } else {
        setUser(null);
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      await signInWithEmailAndPassword(auth, email, password);
    } catch (error) {
      throw error;
    }
  };

  const signOut = async () => {
    try {
      await firebaseSignOut(auth);
    } catch (error) {
      throw error;
    }
  };

  return (
    <AuthContext.Provider value={{ user, loading, signIn, signOut }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => useContext(AuthContext);