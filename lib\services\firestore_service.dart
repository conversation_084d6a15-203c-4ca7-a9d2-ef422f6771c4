import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/gym.dart';
import '../models/user.dart';
import '../models/subscription.dart';
import '../models/attendance.dart';

class FirestoreService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Gyms Collection
  Future<List<Gym>> getGyms() async {
    try {
      print('FirestoreService: Starting to fetch gyms...');

      // Firestore instance is initialized in the constructor

      print('FirestoreService: Accessing gyms collection...');
      final CollectionReference collection = _firestore.collection('gyms');

      print('FirestoreService: Getting documents...');
      final QuerySnapshot snapshot = await collection.get();

      print(
          'FirestoreService: Number of documents found: ${snapshot.docs.length}');

      if (snapshot.docs.isEmpty) {
        print('FirestoreService: No gyms found in the collection');
        return [];
      }

      final List<Gym> gyms = [];
      for (var doc in snapshot.docs) {
        try {
          print('FirestoreService: Processing document ID: ${doc.id}');
          print('FirestoreService: Document data: ${doc.data()}');

          final gym = Gym.fromFirestore(doc);
          print(
              'FirestoreService: Successfully created Gym object: ${gym.toString()}');
          gyms.add(gym);
        } catch (e) {
          print(
              'FirestoreService: Error processing gym document ${doc.id}: $e');
          // Continue processing other documents even if one fails
        }
      }

      print('FirestoreService: Successfully processed ${gyms.length} gyms');
      return gyms;
    } catch (e, stackTrace) {
      print('FirestoreService: Error getting gyms: $e');
      print('FirestoreService: Stack trace: $stackTrace');
      rethrow;
    }
  }

  Future<void> addGym(Gym gym) async {
    try {
      await _firestore.collection('gyms').doc(gym.id).set(gym.toMap());
    } catch (e) {
      print('Error adding gym: $e');
      rethrow;
    }
  }

  // Users Collection
  Future<UserModel?> getUser(String userId) async {
    try {
      print('FirestoreService: Getting user with ID: $userId');
      final doc = await _firestore.collection('users').doc(userId).get();

      if (doc.exists) {
        print('FirestoreService: User found');
        return UserModel.fromFirestore(doc);
      } else {
        print('FirestoreService: User not found, creating new user document');
        // Create a new user document if it doesn't exist
        final user = await _createNewUserDocument(userId);
        return user;
      }
    } catch (e) {
      print('FirestoreService: Error getting user: $e');
      rethrow;
    }
  }

  Future<UserModel> _createNewUserDocument(String userId) async {
    try {
      // Get user email from Firebase Auth if possible
      final authUser = FirebaseAuth.instance.currentUser;
      final email = authUser?.email ?? '<EMAIL>';

      // Use email as name if displayName is null
      String name = authUser?.displayName ?? '';
      if (name.isEmpty && email.isNotEmpty) {
        // Extract name from email (e.g., <EMAIL> -> John Doe)
        name = email.split('@')[0];
        // Capitalize first letter of each word
        name = name.split('.').map((word) {
          if (word.isNotEmpty) {
            return word[0].toUpperCase() + word.substring(1);
          }
          return word;
        }).join(' ');
      }

      if (name.isEmpty) {
        name = 'User';
      }

      print(
          'FirestoreService: Creating new user document with email: $email and name: $name');

      // Create a new user document
      final userData = {
        'email': email,
        'name': name,
        'gymId': null,
        'isVerified': false,
      };

      await _firestore.collection('users').doc(userId).set(userData);

      // Return the new user model
      return UserModel(
        id: userId,
        name: name,
        email: email,
        gymId: null,
        isVerified: false,
        gymIdStatus: '',
      );
    } catch (e) {
      print('FirestoreService: Error creating user document: $e');
      rethrow;
    }
  }

  Future<void> createUser(UserModel user) async {
    try {
      print('FirestoreService: Creating user with ID: ${user.id}');
      await _firestore.collection('users').doc(user.id).set(user.toMap());
      print('FirestoreService: User created successfully');
    } catch (e) {
      print('FirestoreService: Error creating user: $e');
      rethrow;
    }
  }

  Future<void> updateUserGym(String userId, String gymId) async {
    try {
      print('FirestoreService: Updating user $userId with gym $gymId');

      // Check if user document exists
      final userDoc = await _firestore.collection('users').doc(userId).get();

      // Get user email from Firebase Auth
      final authUser = FirebaseAuth.instance.currentUser;
      final email = authUser?.email ?? '<EMAIL>';

      // Use email as name if no name is available
      String name = authUser?.displayName ?? '';
      if (name.isEmpty && email.isNotEmpty) {
        name = email.split('@')[0];
        name = name.split('.').map((word) {
          if (word.isNotEmpty) {
            return word[0].toUpperCase() + word.substring(1);
          }
          return word;
        }).join(' ');
      }

      if (name.isEmpty) {
        name = 'User';
      }

      if (userDoc.exists) {
        // Update existing document
        await _firestore.collection('users').doc(userId).update({
          'gymId': gymId,
          'isVerified': false,
          'email': email, // Ensure email is updated
          'name': name // Ensure name is updated
        });
      } else {
        // Create new document if it doesn't exist
        await _firestore.collection('users').doc(userId).set({
          'email': email,
          'name': name,
          'gymId': gymId,
          'isVerified': false,
        });
      }

      print('FirestoreService: User gym updated successfully');
    } catch (e) {
      print('FirestoreService: Error updating user gym: $e');
      rethrow;
    }
  }

  // Cancel gym request
  Future<void> cancelGymRequest(String userId) async {
    try {
      print('FirestoreService: Cancelling gym request for user $userId');

      // Check if user document exists
      final userDoc = await _firestore.collection('users').doc(userId).get();

      if (userDoc.exists) {
        await _firestore.collection('users').doc(userId).update({
          'gymId': null,
          'isVerified': false,
        });
      } else {
        // Create a new document with no gym
        await _firestore.collection('users').doc(userId).set({
          'email': '<EMAIL>',
          'name': 'User',
          'gymId': null,
          'isVerified': false,
        });
      }

      print('FirestoreService: Gym request cancelled successfully');
    } catch (e) {
      print('FirestoreService: Error cancelling gym request: $e');
      rethrow;
    }
  }

  // Get gym details
  Future<Gym?> getGym(String gymId) async {
    try {
      print('FirestoreService: Getting gym with ID: $gymId');
      final doc = await _firestore.collection('gyms').doc(gymId).get();
      if (doc.exists) {
        print('FirestoreService: Gym found');
        return Gym.fromFirestore(doc);
      }
      print('FirestoreService: Gym not found');
      return null;
    } catch (e) {
      print('FirestoreService: Error getting gym: $e');
      rethrow;
    }
  }

  // Subscriptions
  Future<SubscriptionModel?> getUserSubscription(String userId) async {
    try {
      print('FirestoreService: Getting subscription for user: $userId');
      final querySnapshot = await _firestore
          .collection('subscriptions')
          .where('userId', isEqualTo: userId)
          .where('status', isEqualTo: 'active')
          .orderBy('createdAt', descending: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        print('FirestoreService: Subscription found');
        return SubscriptionModel.fromFirestore(querySnapshot.docs.first);
      }
      print('FirestoreService: No active subscription found');
      return null;
    } catch (e) {
      print('FirestoreService: Error getting subscription: $e');
      rethrow;
    }
  }

  // Stream of user subscription
  Stream<SubscriptionModel?> userSubscriptionStream(String userId) {
    print('FirestoreService: Starting subscription stream for user: $userId');
    return _firestore
        .collection('subscriptions')
        .where('userId', isEqualTo: userId)
        .where('status', isEqualTo: 'active')
        .orderBy('createdAt', descending: true)
        .limit(1)
        .snapshots()
        .map((snapshot) => snapshot.docs.isNotEmpty
            ? SubscriptionModel.fromFirestore(snapshot.docs.first)
            : null);
  }

  // Stream of user data
  Stream<UserModel?> userStream(String userId) {
    print('FirestoreService: Starting user stream for ID: $userId');
    return _firestore
        .collection('users')
        .doc(userId)
        .snapshots()
        .map((doc) => doc.exists ? UserModel.fromFirestore(doc) : null);
  }

  // Attendance methods
  Future<List<AttendanceLog>> getUserAttendanceLogs(String userId) async {
    try {
      print('FirestoreService: Getting attendance logs for user: $userId');
      final querySnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('attendanceLogs')
          .orderBy('timestamp', descending: true)
          .get();

      final logs = querySnapshot.docs
          .map((doc) => AttendanceLog.fromFirestore(doc))
          .toList();

      print('FirestoreService: Found ${logs.length} attendance logs');
      return logs;
    } catch (e) {
      print('FirestoreService: Error getting attendance logs: $e');
      rethrow;
    }
  }

  Stream<List<AttendanceLog>> userAttendanceLogsStream(String userId) {
    print(
        'FirestoreService: Starting attendance logs stream for user: $userId');
    return _firestore
        .collection('users')
        .doc(userId)
        .collection('attendanceLogs')
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => AttendanceLog.fromFirestore(doc))
            .toList());
  }
}
