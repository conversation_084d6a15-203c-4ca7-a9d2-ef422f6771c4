import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../providers/user_provider.dart';
import '../models/gym.dart';
import '../services/firestore_service.dart';
import '../widgets/pending_status_widget.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final FirestoreService _firestoreService = FirestoreService();
  List<Gym> _gyms = [];
  bool _isLoadingGyms = false;
  String? _error;
  bool _hasCheckedVerification = false;

  @override
  void initState() {
    super.initState();
    print('HomeScreen initialized');
    _loadGyms();
  }

  Future<void> _loadGyms() async {
    print('Starting to load gyms...');
    setState(() {
      _isLoadingGyms = true;
      _error = null;
    });

    try {
      final gyms = await _firestoreService.getGyms();
      print('Gyms loaded successfully: ${gyms.length} gyms');
      setState(() {
        _gyms = gyms;
        _isLoadingGyms = false;
      });
    } catch (e) {
      print('Error in _loadGyms: $e');
      setState(() {
        _error = e.toString();
        _isLoadingGyms = false;
      });
    }
  }

  Future<void> _showGymSelectionDialog() async {
    print('Opening gym selection dialog');
    if (_isLoadingGyms) {
      print('Still loading gyms, cannot show dialog');
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select a Gym'),
        content: SizedBox(
          width: double.maxFinite,
          child: _isLoadingGyms
              ? const Center(child: CircularProgressIndicator())
              : _error != null
                  ? Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text('Error: $_error',
                              style: const TextStyle(color: Colors.red)),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                              _loadGyms();
                            },
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    )
                  : _gyms.isEmpty
                      ? const Center(
                          child: Text('No gyms available'),
                        )
                      : ListView.builder(
                          shrinkWrap: true,
                          itemCount: _gyms.length,
                          itemBuilder: (context, index) {
                            final gym = _gyms[index];
                            print('Building gym list item: ${gym.toString()}');
                            return ListTile(
                              title: Text(gym.name),
                              subtitle: Text(gym.location),
                              onTap: () async {
                                print('Gym selected: ${gym.id}');
                                // Store a reference to the scaffold messenger before closing the dialog
                                final scaffoldMessenger =
                                    ScaffoldMessenger.of(context);
                                Navigator.pop(context);
                                try {
                                  await context
                                      .read<UserProvider>()
                                      .subscribeToGym(gym.id);
                                  if (mounted) {
                                    // Use the stored reference
                                    scaffoldMessenger.showSnackBar(
                                      const SnackBar(
                                        content: Text(
                                            'Successfully subscribed to gym!'),
                                      ),
                                    );
                                  }
                                } catch (e) {
                                  print('Error subscribing to gym: $e');
                                  if (mounted) {
                                    // Use the stored reference
                                    scaffoldMessenger.showSnackBar(
                                      SnackBar(
                                        content: Text('Error: ${e.toString()}'),
                                        backgroundColor: Colors.red,
                                      ),
                                    );
                                  }
                                }
                              },
                            );
                          },
                        ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          if (_gyms.isEmpty && _error == null)
            TextButton(
              onPressed: _loadGyms,
              child: const Text('Refresh'),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final userProvider = context.watch<UserProvider>();
    final user = userProvider.user;
    final authService = AuthenticationService();

    // Check if user was just approved by the gym
    if (userProvider.wasJustApproved) {
      print('User was just approved by the gym, navigating to member screen');

      // Use Future.microtask to avoid build issues during the first build
      Future.microtask(() {
        if (mounted) {
          Navigator.pushReplacementNamed(context, '/member');
        }
      });
    }
    // Check if user is verified and has a gym, then navigate to member screen
    else if (!_hasCheckedVerification &&
        user != null &&
        user.gymId != null &&
        user.isVerified) {
      _hasCheckedVerification = true;

      // Use Future.microtask to avoid build issues during the first build
      Future.microtask(() {
        if (mounted) {
          Navigator.pushReplacementNamed(context, '/member');
        }
      });
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Gym Attendance'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              // Clear user data first
              final userProvider =
                  Provider.of<UserProvider>(context, listen: false);
              userProvider.clearUserData();

              // Sign out from Firebase
              await authService.signOut();

              // Navigation will be handled by AuthWrapper
            },
          ),
        ],
      ),
      body: userProvider.isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    'Welcome, ${user?.email ?? 'User'}!',
                    style: Theme.of(context).textTheme.headlineSmall,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 20),
                  if (user?.gymId == null)
                    ElevatedButton(
                      onPressed: _showGymSelectionDialog,
                      child: const Text('Subscribe to a Gym'),
                    )
                  else if (userProvider.hasPendingRequest)
                    const PendingStatusWidget()
                  else if (userProvider.hasApprovedGym)
                    Column(
                      children: [
                        ElevatedButton(
                          onPressed: () {
                            Navigator.pushReplacementNamed(context, '/member');
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('Go to Member Dashboard'),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            // TODO: Implement QR code attendance
                          },
                          child: const Text('Mark Attendance'),
                        ),
                      ],
                    )
                  else
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade100,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        'Waiting for gym verification...',
                        textAlign: TextAlign.center,
                        style: TextStyle(color: Colors.orange),
                      ),
                    ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: () {
                      // TODO: Implement manual attendance tracking
                    },
                    child: const Text('Track Attendance Manually'),
                  ),
                ],
              ),
            ),
    );
  }
}
