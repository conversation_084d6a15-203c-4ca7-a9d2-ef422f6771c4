import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../models/gym.dart';
import '../models/subscription.dart';
import '../services/firestore_service.dart';
import '../services/auth_service.dart';

class UserProvider with ChangeNotifier {
  final FirestoreService _firestoreService = FirestoreService();
  final AuthenticationService _authService = AuthenticationService();
  UserModel? _user;
  Gym? _gym;
  SubscriptionModel? _subscription;
  bool _isLoading = false;
  bool _wasJustApproved = false;
  StreamSubscription? _userSubscription;
  StreamSubscription? _subscriptionStream;

  UserModel? get user => _user;
  Gym? get gym => _gym;
  SubscriptionModel? get subscription => _subscription;
  bool get isLoading => _isLoading;
  bool get isVerified => _user?.isVerified ?? false;
  bool get hasGym => _user?.gymId != null;
  bool get hasPendingRequest =>
      (_user?.gymId != null && !(_user?.isVerified ?? false));
  bool get hasApprovedGym =>
      (_user?.gymId != null && (_user?.isVerified ?? false));

  // Returns true if the user was just approved by the gym
  bool get wasJustApproved {
    if (_wasJustApproved) {
      // Reset the flag after it's been read
      _wasJustApproved = false;
      return true;
    }
    return false;
  }

  Future<void> loadUser() async {
    final currentUser = _authService.currentUser;
    print('UserProvider: Loading user data for UID: ${currentUser?.uid}');

    _isLoading = true;
    notifyListeners();

    try {
      if (currentUser != null) {
        _user = await _firestoreService.getUser(currentUser.uid);
        print('UserProvider: Loaded user: ${_user?.email}');

        // If user has a gym, load gym details
        if (_user?.gymId != null) {
          _gym = await _firestoreService.getGym(_user!.gymId!);

          // If user is already verified, load subscription immediately
          if (_user!.isVerified) {
            print('User is already verified, loading subscription');
            await _loadSubscription(currentUser.uid);
          }
        }

        // Start listening to user changes
        _startUserListener(currentUser.uid);
      }
    } catch (e) {
      print('Error loading user: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void _startUserListener(String userId) {
    // Cancel existing subscription if any
    _userSubscription?.cancel();

    // Start new subscription
    _userSubscription =
        _firestoreService.userStream(userId).listen((updatedUser) {
      if (updatedUser != null) {
        final bool wasApproved =
            !(_user?.isVerified ?? false) && updatedUser.isVerified;

        _user = updatedUser;

        // If user's gym status changed to approved, load subscription
        if (wasApproved) {
          print(
              'User was just approved by gym! Loading subscription and notifying listeners');
          _loadSubscription(userId);

          // Set a flag to indicate that the user was just approved
          // This will be used in the UI to automatically navigate to the subscription screen
          _wasJustApproved = true;
        }

        // If user's gym changed, load new gym details
        if (_gym == null || _gym!.id != updatedUser.gymId) {
          _loadGym(updatedUser.gymId);
        }

        notifyListeners();
      }
    }, onError: (e) {
      print('Error in user stream: $e');
    });
  }

  Future<void> _loadGym(String? gymId) async {
    if (gymId == null) {
      _gym = null;
      notifyListeners();
      return;
    }

    try {
      _gym = await _firestoreService.getGym(gymId);
      notifyListeners();
    } catch (e) {
      print('Error loading gym: $e');
    }
  }

  Future<void> _loadSubscription(String userId) async {
    try {
      _subscription = await _firestoreService.getUserSubscription(userId);

      // Start listening to subscription changes
      _startSubscriptionListener(userId);

      notifyListeners();
    } catch (e) {
      print('Error loading subscription: $e');
    }
  }

  void _startSubscriptionListener(String userId) {
    // Cancel existing subscription if any
    _subscriptionStream?.cancel();

    // Start new subscription
    _subscriptionStream = _firestoreService
        .userSubscriptionStream(userId)
        .listen((updatedSubscription) {
      _subscription = updatedSubscription;
      notifyListeners();
    }, onError: (e) {
      print('Error in subscription stream: $e');
    });
  }

  Future<void> subscribeToGym(String gymId) async {
    _isLoading = true;
    notifyListeners();

    try {
      final currentUser = _authService.currentUser;
      if (currentUser != null) {
        await _firestoreService.updateUserGym(currentUser.uid, gymId);
        await loadUser(); // Reload user data
      }
    } catch (e) {
      print('Error subscribing to gym: $e');
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> cancelGymRequest() async {
    _isLoading = true;
    notifyListeners();

    try {
      final currentUser = _authService.currentUser;
      if (currentUser != null) {
        await _firestoreService.cancelGymRequest(currentUser.uid);
        await loadUser(); // Reload user data
      }
    } catch (e) {
      print('Error cancelling gym request: $e');
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Clear all user data (for logout or user change)
  void clearUserData() {
    print('UserProvider: Clearing all user data');
    _userSubscription?.cancel();
    _subscriptionStream?.cancel();
    _userSubscription = null;
    _subscriptionStream = null;
    _user = null;
    _gym = null;
    _subscription = null;
    _isLoading = false;
    _wasJustApproved = false;
    print('UserProvider: User data cleared successfully');
    notifyListeners();
  }

  @override
  void dispose() {
    // Cancel all stream subscriptions
    _userSubscription?.cancel();
    _subscriptionStream?.cancel();
    super.dispose();
  }
}
