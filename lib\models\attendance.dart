import 'package:cloud_firestore/cloud_firestore.dart';

class AttendanceLog {
  final String id;
  final DateTime timestamp;
  final String gymId;
  final String date; // YYYY-MM-DD format

  AttendanceLog({
    required this.id,
    required this.timestamp,
    required this.gymId,
    required this.date,
  });

  factory AttendanceLog.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AttendanceLog(
      id: doc.id,
      timestamp: data['timestamp'] != null
          ? (data['timestamp'] as Timestamp).toDate()
          : DateTime.now(),
      gymId: data['gymId'] ?? '',
      date: data['date'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'timestamp': Timestamp.fromDate(timestamp),
      'gymId': gymId,
      'date': date,
    };
  }

  @override
  String toString() {
    return 'AttendanceLog(id: $id, timestamp: $timestamp, gymId: $gymId, date: $date)';
  }
}
