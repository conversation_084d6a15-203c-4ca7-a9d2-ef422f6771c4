import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';

class QRService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Generates a daily unique QR code for the user
  static Future<String> generateDailyQRCode(String userId) async {
    final today = DateFormat('yyyy-MM-dd').format(DateTime.now());

    // Create QR payload
    final qrPayload = {
      'uid': userId,
      'date': today,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    // Generate a hash for additional security
    final payloadString = json.encode(qrPayload);
    final bytes = utf8.encode(payloadString + userId + today);
    final hash = sha256.convert(bytes).toString().substring(0, 8);

    qrPayload['hash'] = hash;

    // Store QR code data in Firestore for validation
    await _firestore.collection('gymQRCodes').doc(userId).set({
      'date': today,
      'encodedData': json.encode(qrPayload),
      'createdAt': FieldValue.serverTimestamp(),
      'expiresAt': DateTime.now().add(const Duration(hours: 24)),
    });

    return json.encode(qrPayload);
  }

  /// Validates a QR code payload
  static Future<QRValidationResult> validateQRCode(
      String qrCodeData, String gymId) async {
    try {
      final payload = json.decode(qrCodeData) as Map<String, dynamic>;
      final userId = payload['uid'] as String?;
      final date = payload['date'] as String?;
      final hash = payload['hash'] as String?;

      if (userId == null || date == null || hash == null) {
        return QRValidationResult(
          isValid: false,
          message: 'Invalid QR code format',
        );
      }

      // Check if QR code is for today
      final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
      if (date != today) {
        return QRValidationResult(
          isValid: false,
          message: 'QR code has expired (not for today)',
        );
      }

      // Verify hash
      final expectedPayload = {
        'uid': userId,
        'date': date,
        'timestamp': payload['timestamp'],
      };
      final expectedPayloadString = json.encode(expectedPayload);
      final expectedBytes = utf8.encode(expectedPayloadString + userId + date);
      final expectedHash =
          sha256.convert(expectedBytes).toString().substring(0, 8);

      if (hash != expectedHash) {
        return QRValidationResult(
          isValid: false,
          message: 'Invalid QR code signature',
        );
      }

      // Check if QR code exists in Firestore
      final qrDoc = await _firestore.collection('gymQRCodes').doc(userId).get();
      if (!qrDoc.exists) {
        return QRValidationResult(
          isValid: false,
          message: 'QR code not found in system',
        );
      }

      final qrDocData = qrDoc.data()!;
      if (qrDocData['date'] != today) {
        return QRValidationResult(
          isValid: false,
          message: 'QR code has expired',
        );
      }

      // Fetch user data
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        return QRValidationResult(
          isValid: false,
          message: 'User not found',
        );
      }

      final userData = userDoc.data()!;
      final userGymId = userData['gymId'] as String?;
      final isVerified = userData['isVerified'] as bool? ?? false;

      if (userGymId != gymId) {
        return QRValidationResult(
          isValid: false,
          message: 'User is not subscribed to this gym',
          userData: userData,
        );
      }

      if (!isVerified) {
        return QRValidationResult(
          isValid: false,
          message: 'User subscription is not verified/active',
          userData: userData,
        );
      }

      // Check subscription validity (if subscription fields exist)
      if (userData.containsKey('subscriptionEndDate')) {
        final endDate = (userData['subscriptionEndDate'] as Timestamp).toDate();
        if (DateTime.now().isAfter(endDate)) {
          return QRValidationResult(
            isValid: false,
            message: 'User subscription has expired',
            userData: userData,
          );
        }
      }

      return QRValidationResult(
        isValid: true,
        message: 'QR code is valid',
        userData: userData,
      );
    } catch (e) {
      return QRValidationResult(
        isValid: false,
        message: 'Failed to validate QR code: $e',
      );
    }
  }
}

class QRValidationResult {
  final bool isValid;
  final String message;
  final Map<String, dynamic>? userData;

  QRValidationResult({
    required this.isValid,
    required this.message,
    this.userData,
  });
}
