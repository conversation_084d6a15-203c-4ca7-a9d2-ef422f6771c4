import { NextRequest, NextResponse } from 'next/server';
import { verifyEmailConfig, sendEmail } from '@/lib/email';

export async function GET() {
  try {
    const result = await verifyEmailConfig();
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Email configuration is valid',
      });
    } else {
      return NextResponse.json(
        { error: 'Email configuration error', details: result.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error testing email configuration:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { testEmail } = body;

    if (!testEmail) {
      return NextResponse.json(
        { error: 'Missing testEmail field' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(testEmail)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Send test email
    const testTemplate = {
      subject: 'Test Email from Gym Management System',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2>Email Configuration Test</h2>
          <p>This is a test email to verify that the email system is working correctly.</p>
          <p>If you received this email, the configuration is successful!</p>
          <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
        </div>
      `,
      text: `
        Email Configuration Test
        
        This is a test email to verify that the email system is working correctly.
        If you received this email, the configuration is successful!
        
        Timestamp: ${new Date().toISOString()}
      `,
    };

    const result = await sendEmail(testEmail, testTemplate);

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Test email sent successfully',
        messageId: result.messageId,
      });
    } else {
      return NextResponse.json(
        { error: 'Failed to send test email', details: result.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error sending test email:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
