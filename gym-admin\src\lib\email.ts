import nodemailer from 'nodemailer';

// Email configuration
const emailConfig = {
  host: process.env.EMAIL_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.EMAIL_PORT || '587'),
  secure: process.env.EMAIL_SECURE === 'true',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
  // Add debug logging
  debug: process.env.NODE_ENV === 'development',
  logger: process.env.NODE_ENV === 'development',
};

// Create transporter
const transporter = nodemailer.createTransport(emailConfig);

// Email templates
export const emailTemplates = {
  membershipApproved: (userName: string, gymName: string) => ({
    subject: `🎉 Welcome to ${gymName}! Your membership has been approved`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #22c55e; margin: 0; font-size: 28px;">🎉 Congratulations!</h1>
          </div>
          
          <h2 style="color: #333; margin-bottom: 20px;">Hi ${userName},</h2>
          
          <p style="color: #666; font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
            Great news! Your membership request for <strong style="color: #22c55e;">${gymName}</strong> has been approved!
          </p>
          
          <div style="background-color: #f0fdf4; border-left: 4px solid #22c55e; padding: 20px; margin: 20px 0; border-radius: 5px;">
            <h3 style="color: #15803d; margin: 0 0 10px 0;">What's Next?</h3>
            <ul style="color: #166534; margin: 0; padding-left: 20px;">
              <li>Open your gym app to access your membership</li>
              <li>Generate your daily QR code for gym entry</li>
              <li>Start your fitness journey with us!</li>
            </ul>
          </div>
          
          <p style="color: #666; font-size: 16px; line-height: 1.6; margin-bottom: 30px;">
            Welcome to the ${gymName} family! We're excited to help you achieve your fitness goals.
          </p>
          
          <div style="text-align: center; margin-top: 30px;">
            <p style="color: #999; font-size: 14px; margin: 0;">
              If you have any questions, please contact the gym staff.
            </p>
          </div>
        </div>
      </div>
    `,
    text: `
      Congratulations ${userName}!
      
      Your membership request for ${gymName} has been approved!
      
      What's Next:
      - Open your gym app to access your membership
      - Generate your daily QR code for gym entry
      - Start your fitness journey with us!
      
      Welcome to the ${gymName} family! We're excited to help you achieve your fitness goals.
      
      If you have any questions, please contact the gym staff.
    `,
  }),

  membershipRejected: (userName: string, gymName: string, reason?: string) => ({
    subject: `Update on your ${gymName} membership request`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #ef4444; margin: 0; font-size: 28px;">Membership Request Update</h1>
          </div>
          
          <h2 style="color: #333; margin-bottom: 20px;">Hi ${userName},</h2>
          
          <p style="color: #666; font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
            Thank you for your interest in joining <strong>${gymName}</strong>.
          </p>
          
          <div style="background-color: #fef2f2; border-left: 4px solid #ef4444; padding: 20px; margin: 20px 0; border-radius: 5px;">
            <p style="color: #dc2626; margin: 0; font-weight: 500;">
              Unfortunately, we are unable to approve your membership request at this time.
            </p>
            ${reason ? `<p style="color: #dc2626; margin: 10px 0 0 0;">Reason: ${reason}</p>` : ''}
          </div>
          
          <div style="background-color: #f0f9ff; border-left: 4px solid #3b82f6; padding: 20px; margin: 20px 0; border-radius: 5px;">
            <h3 style="color: #1d4ed8; margin: 0 0 10px 0;">What You Can Do:</h3>
            <ul style="color: #1e40af; margin: 0; padding-left: 20px;">
              <li>Contact the gym directly for more information</li>
              <li>Consider applying to other gyms in your area</li>
              <li>You can reapply in the future if circumstances change</li>
            </ul>
          </div>
          
          <p style="color: #666; font-size: 16px; line-height: 1.6; margin-bottom: 30px;">
            We appreciate your interest and wish you the best in finding the right fitness solution for you.
          </p>
          
          <div style="text-align: center; margin-top: 30px;">
            <p style="color: #999; font-size: 14px; margin: 0;">
              If you have any questions, please contact ${gymName} directly.
            </p>
          </div>
        </div>
      </div>
    `,
    text: `
      Hi ${userName},
      
      Thank you for your interest in joining ${gymName}.
      
      Unfortunately, we are unable to approve your membership request at this time.
      ${reason ? `Reason: ${reason}` : ''}
      
      What You Can Do:
      - Contact the gym directly for more information
      - Consider applying to other gyms in your area
      - You can reapply in the future if circumstances change
      
      We appreciate your interest and wish you the best in finding the right fitness solution for you.
      
      If you have any questions, please contact ${gymName} directly.
    `,
  }),
};

// Email sending function
export async function sendEmail(to: string, template: { subject: string; html: string; text: string }) {
  try {
    const mailOptions = {
      from: {
        name: process.env.EMAIL_FROM_NAME || 'Gym Management',
        address: process.env.EMAIL_FROM || process.env.EMAIL_USER || '',
      },
      to,
      subject: template.subject,
      html: template.html,
      text: template.text,
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('Email sent successfully:', result.messageId);
    return { success: true, messageId: result.messageId };
  } catch (error) {
    console.error('Error sending email:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// Verify email configuration
export async function verifyEmailConfig() {
  try {
    await transporter.verify();
    console.log('Email configuration is valid');
    return { success: true };
  } catch (error) {
    console.error('Email configuration error:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}
