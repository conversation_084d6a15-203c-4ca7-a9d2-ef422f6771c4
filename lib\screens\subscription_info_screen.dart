import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/user_provider.dart';
import '../models/subscription.dart';
import '../models/gym.dart';

class SubscriptionInfoScreen extends StatelessWidget {
  const SubscriptionInfoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);
    final subscription = userProvider.subscription;
    final gym = userProvider.gym;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Subscription Info'),
      ),
      body: userProvider.isLoading
          ? const Center(child: CircularProgressIndicator())
          : subscription == null
              ? _buildNoSubscriptionView(context)
              : _buildSubscriptionDetails(context, subscription, gym),
    );
  }

  Widget _buildNoSubscriptionView(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);
    final gym = userProvider.gym;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.card_membership_outlined,
              size: 80,
              color: Colors.grey,
            ),
            const SizedBox(height: 24),
            Text(
              'No Active Subscription',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'You are verified at ${gym?.name ?? 'your gym'}, but no subscription details were found. Please contact the gym staff to activate your subscription.',
              textAlign: TextAlign.center,
            ),
            if (gym != null) ...[
              const SizedBox(height: 24),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (gym.logoUrl != null && gym.logoUrl!.isNotEmpty)
                        Center(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              gym.logoUrl!,
                              height: 100,
                              width: 100,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return const Icon(
                                  Icons.fitness_center,
                                  size: 80,
                                  color: Colors.grey,
                                );
                              },
                            ),
                          ),
                        ),
                      const SizedBox(height: 16),
                      Text(
                        gym.name,
                        style: Theme.of(context).textTheme.titleLarge,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      _buildInfoRow(Icons.location_on, gym.location),
                      const SizedBox(height: 8),
                      _buildInfoRow(Icons.info, gym.description),
                    ],
                  ),
                ),
              ),
            ],
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () {
                Navigator.pushReplacementNamed(context, '/member');
              },
              child: const Text('Go to Member Dashboard'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubscriptionDetails(
    BuildContext context,
    SubscriptionModel subscription,
    Gym? gym
  ) {
    final dateFormat = DateFormat('MMM dd, yyyy');
    final isActive = subscription.isActive;

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Subscription Status Card
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(
                  color: isActive ? Colors.green.shade300 : Colors.red.shade300,
                  width: 2,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Icon(
                      isActive ? Icons.check_circle : Icons.cancel,
                      color: isActive ? Colors.green : Colors.red,
                      size: 48,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      isActive ? 'Active' : 'Expired',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            color: isActive ? Colors.green : Colors.red,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    if (isActive) ...[
                      const SizedBox(height: 8),
                      Text(
                        '${subscription.daysRemaining} days remaining',
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Gym Info
            if (gym != null) ...[
              Text(
                'Gym Information',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (gym.logoUrl != null && gym.logoUrl!.isNotEmpty)
                        Center(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              gym.logoUrl!,
                              height: 100,
                              width: 100,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return const Icon(
                                  Icons.fitness_center,
                                  size: 80,
                                  color: Colors.grey,
                                );
                              },
                            ),
                          ),
                        ),
                      const SizedBox(height: 16),
                      Text(
                        gym.name,
                        style: Theme.of(context).textTheme.titleLarge,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      _buildInfoRow(Icons.location_on, gym.location),
                      const SizedBox(height: 8),
                      _buildInfoRow(Icons.info, gym.description),
                    ],
                  ),
                ),
              ),
            ],

            const SizedBox(height: 24),

            // Subscription Details
            Text(
              'Subscription Details',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    _buildInfoRow(
                      Icons.calendar_today,
                      'Start Date: ${dateFormat.format(subscription.startDate)}',
                    ),
                    const SizedBox(height: 16),
                    _buildInfoRow(
                      Icons.event,
                      'End Date: ${dateFormat.format(subscription.endDate)}',
                    ),
                    const SizedBox(height: 16),
                    _buildInfoRow(
                      Icons.access_time,
                      'Created: ${dateFormat.format(subscription.createdAt)}',
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 32),

            // Action Button
            if (!isActive)
              ElevatedButton.icon(
                onPressed: () {
                  // TODO: Implement renewal logic
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please contact gym staff to renew your subscription'),
                    ),
                  );
                },
                icon: const Icon(Icons.refresh),
                label: const Text('Renew Subscription'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String text) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 20, color: Colors.blue),
        const SizedBox(width: 8),
        Expanded(
          child: Text(text),
        ),
      ],
    );
  }
}
