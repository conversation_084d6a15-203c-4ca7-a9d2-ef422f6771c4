rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read access to gyms collection and its subcollections
    match /gyms/{gymId}/{document=**} {
      allow read: if true;
      // Allow gym staff to update their own gym
      allow write: if request.auth != null &&
                    exists(/databases/$(database)/documents/gymStaffs/$(request.auth.uid)) &&
                    get(/databases/$(database)/documents/gymStaffs/$(request.auth.uid)).data.gymId == gymId;
    }

    // Allow authenticated users to read and write to their own user document
    // Also allow gym staff to read and write to all user documents
    match /users/{userId} {
      allow read, write: if request.auth != null &&
                          (request.auth.uid == userId ||
                           exists(/databases/$(database)/documents/gymStaffs/$(request.auth.uid)));

      // Allow users to read their own attendance logs
      // Also allow gym staff to read and write attendance logs for users in their gym
      match /attendanceLogs/{logId} {
        allow read: if request.auth != null &&
                    (request.auth.uid == userId ||
                     exists(/databases/$(database)/documents/gymStaffs/$(request.auth.uid)));
        allow write: if request.auth != null &&
                     exists(/databases/$(database)/documents/gymStaffs/$(request.auth.uid));
      }
    }

    // Allow gym staff to read and write gymStaffs collection
    match /gymStaffs/{staffId} {
      allow read, write: if request.auth != null;
    }

    // Allow users to read and write their own QR codes
    // Also allow gym staff to read and write QR codes for users in their gym
    match /gymQRCodes/{userId} {
      allow read, write: if request.auth != null &&
                          request.auth.uid == userId;
      allow read, write: if request.auth != null &&
                          exists(/databases/$(database)/documents/gymStaffs/$(request.auth.uid));
    }

    // Default deny
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
