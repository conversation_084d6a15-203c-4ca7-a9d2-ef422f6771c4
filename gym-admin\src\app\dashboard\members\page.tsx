'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { db } from '@/lib/firebase';
import { collection, query, where, getDocs, doc, updateDoc, getDoc } from 'firebase/firestore';
import DashboardLayout from '@/components/DashboardLayout';
import toast from 'react-hot-toast';

interface User {
  id: string;
  name: string;
  email: string;
  gymId: string | null;
  isVerified: boolean;
}

interface GymData {
  name: string;
  description: string;
  location: string;
  logoUrl?: string;
}

export default function MembersPage() {
  const { user } = useAuth();
  const [pendingMembers, setPendingMembers] = useState<User[]>([]);
  const [acceptedMembers, setAcceptedMembers] = useState<User[]>([]);
  const [gymData, setGymData] = useState<GymData | null>(null);
  const [loading, setLoading] = useState(true);
  const [processingIds, setProcessingIds] = useState<string[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      if (!user?.gymId) {
        setLoading(false);
        return;
      }

      try {
        // Fetch gym data
        const gymDoc = await getDoc(doc(db, 'gyms', user.gymId));
        if (gymDoc.exists()) {
          setGymData(gymDoc.data() as GymData);
        }

        // Fetch pending members (users who requested to join this gym)
        const pendingQuery = query(
          collection(db, 'users'),
          where('gymId', '==', user.gymId),
          where('isVerified', '==', false)
        );

        // Fetch accepted members (users who are verified and belong to this gym)
        const acceptedQuery = query(
          collection(db, 'users'),
          where('gymId', '==', user.gymId),
          where('isVerified', '==', true)
        );

        const [pendingSnapshot, acceptedSnapshot] = await Promise.all([
          getDocs(pendingQuery),
          getDocs(acceptedQuery)
        ]);

        const pendingData = pendingSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as User[];

        const acceptedData = acceptedSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as User[];

        setPendingMembers(pendingData);
        setAcceptedMembers(acceptedData);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error('Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user?.gymId]);

  // Send email notification
  const sendEmailNotification = async (
    type: 'approved' | 'rejected',
    userEmail: string,
    userName: string,
    reason?: string
  ) => {
    if (!gymData?.name) {
      console.warn('Gym name not available, skipping email notification');
      return;
    }

    try {
      const endpoint = type === 'approved'
        ? '/api/email/membership-approved'
        : '/api/email/membership-rejected';

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userEmail,
          userName,
          gymName: gymData.name,
          ...(reason && { reason }),
        }),
      });

      const result = await response.json();

      if (result.success) {
        console.log(`${type} email sent successfully to ${userEmail}`);
      } else {
        console.error(`Failed to send ${type} email:`, result.error);
        toast.error(`Failed to send ${type} email notification`);
      }
    } catch (error) {
      console.error(`Error sending ${type} email:`, error);
      toast.error(`Failed to send ${type} email notification`);
    }
  };

  // Accept a pending member
  const handleAcceptMember = async (memberId: string) => {
    if (!user?.gymId) return;

    setProcessingIds(prev => [...prev, memberId]);

    try {
      // Get member data before updating
      const memberToAccept = pendingMembers.find(m => m.id === memberId);

      await updateDoc(doc(db, 'users', memberId), {
        gymId: user.gymId,
        isVerified: true
      });

      // Update local state
      if (memberToAccept) {
        const updatedMember = { ...memberToAccept, gymId: user.gymId, isVerified: true };
        setPendingMembers(prev => prev.filter(m => m.id !== memberId));
        setAcceptedMembers(prev => [...prev, updatedMember]);

        // Send approval email notification
        await sendEmailNotification(
          'approved',
          memberToAccept.email,
          memberToAccept.name
        );
      }

      toast.success('Member accepted successfully');
    } catch (error) {
      console.error('Error accepting member:', error);
      toast.error('Failed to accept member');
    } finally {
      setProcessingIds(prev => prev.filter(id => id !== memberId));
    }
  };

  // Deny a pending member
  const handleDenyMember = async (memberId: string) => {
    setProcessingIds(prev => [...prev, memberId]);

    try {
      // Get member data before removing
      const memberToDeny = pendingMembers.find(m => m.id === memberId);

      // Reset their gym request so they can apply to other gyms
      await updateDoc(doc(db, 'users', memberId), {
        gymId: null,
        isVerified: false
      });

      // Update local state
      setPendingMembers(prev => prev.filter(m => m.id !== memberId));

      // Send rejection email notification
      if (memberToDeny) {
        await sendEmailNotification(
          'rejected',
          memberToDeny.email,
          memberToDeny.name,
          'Your membership request has been reviewed and we are unable to approve it at this time.'
        );
      }

      toast.success('Member request denied');
    } catch (error) {
      console.error('Error denying member:', error);
      toast.error('Failed to deny member');
    } finally {
      setProcessingIds(prev => prev.filter(id => id !== memberId));
    }
  };

  // Deactivate a member's subscription
  const handleDeactivateMember = async (memberId: string) => {
    setProcessingIds(prev => [...prev, memberId]);

    try {
      await updateDoc(doc(db, 'users', memberId), {
        gymId: null,
        isVerified: false
      });

      // Update local state
      setAcceptedMembers(prev => prev.filter(m => m.id !== memberId));
      toast.success('Member subscription deactivated');
    } catch (error) {
      console.error('Error deactivating member:', error);
      toast.error('Failed to deactivate member');
    } finally {
      setProcessingIds(prev => prev.filter(id => id !== memberId));
    }
  };

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto">
        {loading ? (
          <div className="mt-6 flex justify-center">
            <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-indigo-600"></div>
          </div>
        ) : (
          <>
            {/* Accepted Members Section */}
            <div className="sm:flex sm:items-center sm:justify-between">
              <div className="sm:flex-auto">
                <h1 className="text-2xl font-semibold text-gray-900">Members</h1>
                <p className="mt-2 text-sm text-gray-700">
                  A list of all verified members in your gym.
                </p>
              </div>
              <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
                <span className="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-green-100 text-green-800">
                  {acceptedMembers.length} Active Members
                </span>
              </div>
            </div>

            {acceptedMembers.length > 0 ? (
              <div className="mt-6 flow-root">
                <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                  <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                    <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                      <table className="min-w-full divide-y divide-gray-300">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">
                              Name
                            </th>
                            <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                              Email
                            </th>
                            <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                              <span className="sr-only">Actions</span>
                            </th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200 bg-white">
                          {acceptedMembers.map((member) => (
                            <tr key={member.id}>
                              <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                                {member.name}
                              </td>
                              <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                {member.email}
                              </td>
                              <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                                <button
                                  onClick={() => handleDeactivateMember(member.id)}
                                  disabled={processingIds.includes(member.id)}
                                  className="text-red-600 hover:text-red-900 disabled:opacity-50"
                                >
                                  {processingIds.includes(member.id) ? 'Processing...' : 'Deactivate Subscription'}
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="mt-6 bg-white shadow overflow-hidden sm:rounded-lg p-6 text-center text-gray-500">
                No active members found.
              </div>
            )}

            {/* Pending Members Section */}
            <div className="mt-12 sm:flex sm:items-center">
              <div className="sm:flex-auto">
                <h2 className="text-xl font-semibold text-gray-900">Pending Requests</h2>
                <p className="mt-2 text-sm text-gray-700">
                  Users who have requested to join your gym.
                </p>
              </div>
            </div>

            {pendingMembers.length > 0 ? (
              <div className="mt-6 flow-root">
                <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                  <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                    <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                      <table className="min-w-full divide-y divide-gray-300">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">
                              Name
                            </th>
                            <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                              Email
                            </th>
                            <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                              <span className="sr-only">Actions</span>
                            </th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200 bg-white">
                          {pendingMembers.map((member) => (
                            <tr key={member.id}>
                              <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                                {member.name}
                              </td>
                              <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                {member.email}
                              </td>
                              <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                                <div className="flex justify-end space-x-2">
                                  <button
                                    onClick={() => handleAcceptMember(member.id)}
                                    disabled={processingIds.includes(member.id)}
                                    className="text-green-600 hover:text-green-900 disabled:opacity-50"
                                  >
                                    {processingIds.includes(member.id) ? 'Processing...' : 'Accept'}
                                  </button>
                                  <button
                                    onClick={() => handleDenyMember(member.id)}
                                    disabled={processingIds.includes(member.id)}
                                    className="text-red-600 hover:text-red-900 disabled:opacity-50"
                                  >
                                    {processingIds.includes(member.id) ? 'Processing...' : 'Deny'}
                                  </button>
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="mt-6 bg-white shadow overflow-hidden sm:rounded-lg p-6 text-center text-gray-500">
                No pending membership requests.
              </div>
            )}
          </>
        )}
      </div>
    </DashboardLayout>
  );
}