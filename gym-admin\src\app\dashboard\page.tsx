'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { db } from '@/lib/firebase';
import { doc, getDoc, updateDoc, setDoc, Timestamp } from 'firebase/firestore';
import DashboardLayout from '@/components/DashboardLayout';
import toast from 'react-hot-toast';

interface GymData {
  name: string;
  description: string;
  location: string;
  logoUrl?: string;
  openTime?: string;
  closeTime?: string;
  phoneNumber?: string;
  email?: string;
  createdAt?: Timestamp;
}

export default function DashboardPage() {
  const { user } = useAuth();
  const [gymData, setGymData] = useState<GymData | null>(null);
  const [formData, setFormData] = useState<GymData | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    const fetchGymData = async () => {
      if (!user?.gymId) {
        setLoading(false);
        return;
      }

      try {
        // Ensure gymId is a string
        const gymId = typeof user.gymId === 'string' ? user.gymId : String(user.gymId);

        // Try with the original gymId
        let gymDoc = await getDoc(doc(db, 'gyms', gymId));

        // If not found, try with lowercase version
        if (!gymDoc.exists() && gymId !== gymId.toLowerCase()) {
          gymDoc = await getDoc(doc(db, 'gyms', gymId.toLowerCase()));
        }

        // If still not found, create a default gym document
        if (!gymDoc.exists()) {
          if (confirm(`No gym document found with ID "${gymId}". Would you like to create a default gym?`)) {
            try {
              // Create a default gym document
              const defaultGym = {
                name: 'Test Gym',
                description: 'Open 24/7, Crossfit, Yoga, Sauna',
                location: 'New Cairo, Egypt',
                logoUrl: 'https://www.pinterest.com/pin/615937684286',
                createdAt: Timestamp.now()
              };

              await setDoc(doc(db, 'gyms', gymId), defaultGym);

              // Reload the page to apply changes
              window.location.reload();
              return;
            } catch (error) {
              console.error('Error creating gym document:', error);
              toast.error('Failed to create gym document');
            }
          }
        }

        if (gymDoc.exists()) {
          const data = gymDoc.data() as GymData;
          setGymData(data);
          setFormData(data);
        }
      } catch (error) {
        toast.error('Failed to load gym data');
      } finally {
        setLoading(false);
      }
    };

    fetchGymData();
  }, [user?.gymId]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => prev ? { ...prev, [name]: value } : null);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user?.gymId || !formData) return;

    setSaving(true);
    try {
      // Remove createdAt from the update data
      const { createdAt, ...updateData } = formData;

      await updateDoc(doc(db, 'gyms', user.gymId), updateData);
      setGymData(formData);
      setIsEditing(false);
      toast.success('Gym information updated successfully');
    } catch (error) {
      toast.error('Failed to update gym information');
    } finally {
      setSaving(false);
    }
  };

  // Add a fallback UI when no gym data is available
  if (!user?.gymId) {
    return (
      <DashboardLayout>
        <div className="max-w-7xl mx-auto">
          <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
          <div className="mt-6 bg-white shadow overflow-hidden sm:rounded-lg p-6">
            <p className="text-gray-500">No gym associated with this account. Please contact your administrator.</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto">
        <div className="sm:flex sm:items-center sm:justify-between">
          <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
          {gymData && !isEditing && (
            <button
              type="button"
              onClick={() => setIsEditing(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Edit Gym Information
            </button>
          )}
          {isEditing && (
            <div className="flex space-x-2">
              <button
                type="button"
                onClick={() => {
                  setIsEditing(false);
                  setFormData(gymData);
                }}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                form="gym-form"
                disabled={saving}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {saving ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          )}
        </div>

        {loading ? (
          <div className="mt-6 flex justify-center">
            <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-indigo-600"></div>
          </div>
        ) : gymData ? (
          <div className="mt-6 bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Gym Information
              </h3>
            </div>

            {isEditing ? (
              <div className="border-t border-gray-200">
                <form id="gym-form" onSubmit={handleSubmit}>
                  <div className="px-4 py-5 sm:p-6">
                    <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                      <div className="sm:col-span-3">
                        <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                          Name
                        </label>
                        <div className="mt-1">
                          <input
                            type="text"
                            name="name"
                            id="name"
                            value={formData?.name || ''}
                            onChange={handleInputChange}
                            className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                            required
                          />
                        </div>
                      </div>

                      <div className="sm:col-span-3">
                        <label htmlFor="location" className="block text-sm font-medium text-gray-700">
                          Location
                        </label>
                        <div className="mt-1">
                          <input
                            type="text"
                            name="location"
                            id="location"
                            value={formData?.location || ''}
                            onChange={handleInputChange}
                            className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                            required
                          />
                        </div>
                      </div>

                      <div className="sm:col-span-3">
                        <label htmlFor="openTime" className="block text-sm font-medium text-gray-700">
                          Opening Time
                        </label>
                        <div className="mt-1">
                          <input
                            type="text"
                            name="openTime"
                            id="openTime"
                            placeholder="e.g. 9:00 AM"
                            value={formData?.openTime || ''}
                            onChange={handleInputChange}
                            className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                          />
                        </div>
                      </div>

                      <div className="sm:col-span-3">
                        <label htmlFor="closeTime" className="block text-sm font-medium text-gray-700">
                          Closing Time
                        </label>
                        <div className="mt-1">
                          <input
                            type="text"
                            name="closeTime"
                            id="closeTime"
                            placeholder="e.g. 10:00 PM"
                            value={formData?.closeTime || ''}
                            onChange={handleInputChange}
                            className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                          />
                        </div>
                      </div>

                      <div className="sm:col-span-3">
                        <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700">
                          Phone Number
                        </label>
                        <div className="mt-1">
                          <input
                            type="text"
                            name="phoneNumber"
                            id="phoneNumber"
                            value={formData?.phoneNumber || ''}
                            onChange={handleInputChange}
                            className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                          />
                        </div>
                      </div>

                      <div className="sm:col-span-3">
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                          Email
                        </label>
                        <div className="mt-1">
                          <input
                            type="email"
                            name="email"
                            id="email"
                            value={formData?.email || ''}
                            onChange={handleInputChange}
                            className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                          />
                        </div>
                      </div>

                      <div className="sm:col-span-6">
                        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                          Description
                        </label>
                        <div className="mt-1">
                          <textarea
                            id="description"
                            name="description"
                            rows={3}
                            value={formData?.description || ''}
                            onChange={handleInputChange}
                            className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                          />
                        </div>
                      </div>

                      <div className="sm:col-span-6">
                        <label htmlFor="logoUrl" className="block text-sm font-medium text-gray-700">
                          Logo URL
                        </label>
                        <div className="mt-1">
                          <input
                            type="text"
                            name="logoUrl"
                            id="logoUrl"
                            value={formData?.logoUrl || ''}
                            onChange={handleInputChange}
                            className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                          />
                        </div>
                      </div>

                      {gymData.createdAt && (
                        <div className="sm:col-span-6">
                          <label className="block text-sm font-medium text-gray-700">
                            Created At
                          </label>
                          <div className="mt-1">
                            <input
                              type="text"
                              value={gymData.createdAt.toDate().toLocaleString()}
                              className="shadow-sm bg-gray-50 block w-full sm:text-sm border-gray-300 rounded-md"
                              disabled
                            />
                          </div>
                          <p className="mt-1 text-xs text-gray-500">This field cannot be edited</p>
                        </div>
                      )}
                    </div>
                  </div>
                </form>
              </div>
            ) : (
              <div className="border-t border-gray-200">
                <dl>
                  <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Name</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:col-span-2">
                      {gymData.name}
                    </dd>
                  </div>
                  <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Location</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:col-span-2">
                      {gymData.location}
                    </dd>
                  </div>
                  {gymData.openTime && (
                    <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Opening Time</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:col-span-2">
                        {gymData.openTime}
                      </dd>
                    </div>
                  )}
                  {gymData.closeTime && (
                    <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Closing Time</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:col-span-2">
                        {gymData.closeTime}
                      </dd>
                    </div>
                  )}
                  {gymData.phoneNumber && (
                    <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Phone Number</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:col-span-2">
                        {gymData.phoneNumber}
                      </dd>
                    </div>
                  )}
                  {gymData.email && (
                    <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Email</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:col-span-2">
                        {gymData.email}
                      </dd>
                    </div>
                  )}
                  <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">
                      Description
                    </dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:col-span-2">
                      {gymData.description}
                    </dd>
                  </div>
                  {gymData.logoUrl && (
                    <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Logo</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:col-span-2">
                        <img
                          src={gymData.logoUrl}
                          alt="Gym Logo"
                          className="h-32 w-32 object-cover rounded-lg"
                        />
                      </dd>
                    </div>
                  )}
                  {gymData.createdAt && (
                    <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Created At</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:col-span-2">
                        {gymData.createdAt.toDate().toLocaleString()}
                      </dd>
                    </div>
                  )}
                </dl>
              </div>
            )}
          </div>
        ) : (
          <div className="mt-6 bg-white shadow overflow-hidden sm:rounded-lg p-6">
            <p className="text-gray-500">No gym data available. Please try again later.</p>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}