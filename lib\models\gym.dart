import 'package:cloud_firestore/cloud_firestore.dart';

class Gym {
  final String id;
  final String name;
  final String location;
  final String description;
  final String? logoUrl;
  final DateTime createdAt;

  Gym({
    required this.id,
    required this.name,
    required this.location,
    required this.description,
    this.logoUrl,
    required this.createdAt,
  });

  factory Gym.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Gym(
      id: doc.id,
      name: data['name'] ?? '',
      location: data['location'] ?? '',
      description: data['description'] ?? '',
      logoUrl: data['logoUrl'],
      createdAt: data['createdAt'] != null
          ? (data['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'location': location,
      'description': description,
      'logoUrl': logoUrl,
      'createdAt': createdAt != null ? Timestamp.fromDate(createdAt) : null,
    };
  }

  @override
  String toString() {
    return 'Gym(id: $id, name: $name, location: $location, description: $description)';
  }
}
