'use client';

import { useState } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import QRCode from 'qrcode.react';

export default function QRGeneratorPage() {
  const [qrValue, setQrValue] = useState('');

  const handleGenerateQR = (e: React.FormEvent) => {
    e.preventDefault();
    // Here you would typically generate a unique identifier or URL
    const uniqueId = Math.random().toString(36).substring(7);
    setQrValue(`https://yourgym.com/checkin/${uniqueId}`);
  };

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900">QR Code Generator</h1>
            <p className="mt-2 text-sm text-gray-700">
              Generate QR codes for gym check-ins and member identification.
            </p>
          </div>
        </div>

        <div className="mt-6 bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <form onSubmit={handleGenerateQR} className="space-y-6">
              <div>
                <button
                  type="submit"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Generate New QR Code
                </button>
              </div>
            </form>

            {qrValue && (
              <div className="mt-6">
                <div className="bg-gray-50 p-4 rounded-lg inline-block">
                  <QRCode
                    value={qrValue}
                    size={256}
                    level="H"
                    includeMargin={true}
                  />
                </div>
                <div className="mt-4">
                  <p className="text-sm text-gray-500">QR Code Value:</p>
                  <p className="mt-1 text-sm font-medium text-gray-900 break-all">
                    {qrValue}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
} 